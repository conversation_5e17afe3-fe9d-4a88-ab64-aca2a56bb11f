#!/bin/bash

# NuclearStory - Development Script
echo "🛠️  Starting NuclearStory in development mode..."

# Start only infrastructure (databases, redis)
echo "📦 Starting infrastructure services..."
docker-compose up -d postgres-auth postgres-saves redis nginx

echo "⏳ Waiting for infrastructure..."
sleep 10

echo ""
echo "🔧 Infrastructure ready! Now start services manually:"
echo ""
echo "Backend services:"
echo "   cd back/auth-service && npm run start:dev"
echo "   cd back/game-engine-service && npm run start:dev"
echo "   cd back/story-service && npm run start:dev"
echo "   cd back/save-service && npm run start:dev"
echo ""
echo "Frontend:"
echo "   cd front && npm run dev"
echo ""
echo "📊 Check status: ./scripts/status.sh"
echo "🛑 Stop infrastructure: docker-compose down"
