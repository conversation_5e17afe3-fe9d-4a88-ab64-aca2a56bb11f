#!/bin/bash

# NuclearStory - Development Start Script with Hot Reload
echo "🛠️  Starting NuclearStory in DEVELOPMENT mode with hot reload..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 .env file created with default values for development."
fi

# Load environment variables
source .env

echo "🔧 Building development images..."

# Build all development images
docker-compose -f docker-compose.dev.yml build

echo "🗄️  Starting databases and Redis..."
docker-compose -f docker-compose.dev.yml up -d postgres-auth postgres-saves redis

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
sleep 15

echo "🤖 Starting AI service..."
docker-compose -f docker-compose.dev.yml up -d ai-service

# Wait for AI service
echo "⏳ Waiting for AI service..."
sleep 10

echo "🔧 Starting backend services with hot reload..."
docker-compose -f docker-compose.dev.yml up -d auth-service game-engine-service story-service save-service

# Wait for backend services
echo "⏳ Waiting for backend services..."
sleep 20

echo "🎨 Starting frontend with hot reload..."
docker-compose -f docker-compose.dev.yml up -d frontend

echo "🌐 Starting nginx proxy..."
docker-compose -f docker-compose.dev.yml up -d nginx

echo ""
echo "✅ NuclearStory development environment is ready!"
echo ""
echo "🌐 Access points:"
echo "   🎮 Game (Frontend): http://localhost:3000"
echo "   🌍 API Gateway: http://localhost:80"
echo "   🔐 Auth Service: http://localhost:3001/api/docs"
echo "   🎯 Game Engine: http://localhost:3002/api/docs"
echo "   📖 Story Service: http://localhost:3003/api/docs"
echo "   💾 Save Service: http://localhost:3004/api/docs"
echo "   🤖 AI Service: http://localhost:3005/docs"
echo ""
echo "🔥 HOT RELOAD ENABLED:"
echo "   - Edit files in /front, /back, /ai folders"
echo "   - Changes will be automatically reflected"
echo "   - No need to rebuild containers"
echo ""
echo "📊 Monitor with: ./scripts/dev-logs.sh"
echo "📈 Check status: ./scripts/status.sh"
echo "🛑 Stop with: ./scripts/dev-stop.sh"
