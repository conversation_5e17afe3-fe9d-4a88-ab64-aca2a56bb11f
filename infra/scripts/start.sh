#!/bin/bash

# NuclearStory - Start Script
echo "🚀 Starting NuclearStory..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before running again."
    exit 1
fi

# Load environment variables
source .env

echo "🔧 Building and starting services..."

# Start infrastructure services first
echo "📦 Starting databases and Redis..."
docker-compose up -d postgres-auth postgres-saves redis

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
sleep 10

# Start backend services
echo "🔧 Starting backend services..."
docker-compose up -d auth-service game-engine-service story-service save-service

# Wait for backend services
echo "⏳ Waiting for backend services..."
sleep 15

# Start frontend and nginx
echo "🎨 Starting frontend and proxy..."
docker-compose up -d frontend nginx

echo "✅ NuclearStory is starting up!"
echo ""
echo "🌐 Access points:"
echo "   Frontend: http://localhost:3000"
echo "   API Gateway: http://localhost:80"
echo "   Auth Service: http://localhost:3001/api/docs"
echo "   Game Engine: http://localhost:3002/api/docs"
echo "   Story Service: http://localhost:3003/api/docs"
echo "   Save Service: http://localhost:3004/api/docs"
echo ""
echo "📊 Check status with: ./scripts/status.sh"
echo "🛑 Stop with: ./scripts/stop.sh"
