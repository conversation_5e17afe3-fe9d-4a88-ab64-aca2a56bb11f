#!/bin/bash

# NuclearStory - Status Script
echo "📊 NuclearStory Status"
echo "===================="

# Check Docker containers
echo ""
echo "🐳 Docker Containers:"
docker-compose ps

# Check service health
echo ""
echo "🏥 Service Health Checks:"

services=("auth-service:3001" "game-engine-service:3002" "story-service:3003" "save-service:3004" "ai-service:3005")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "   ✅ $name (port $port) - Healthy"
    else
        echo "   ❌ $name (port $port) - Unhealthy"
    fi
done

# Check frontend
if curl -s -f "http://localhost:3000" > /dev/null 2>&1; then
    echo "   ✅ frontend (port 3000) - Healthy"
else
    echo "   ❌ frontend (port 3000) - Unhealthy"
fi

# Check nginx
if curl -s -f "http://localhost:80/health" > /dev/null 2>&1; then
    echo "   ✅ nginx (port 80) - Healthy"
else
    echo "   ❌ nginx (port 80) - Unhealthy"
fi

echo ""
echo "📝 View logs with: docker-compose logs [service-name]"
echo "🔄 Restart service: docker-compose restart [service-name]"
