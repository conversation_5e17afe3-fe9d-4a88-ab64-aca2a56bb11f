# Infrastructure - NuclearStory

Docker and infrastructure configuration for the NuclearStory project.

## Contents

- `docker-compose.yml` - Main orchestration file
- `nginx/` - Reverse proxy configuration
- `postgres/` - Database initialization scripts
- `env/` - Environment variable templates

## Services

### Databases
- **postgres-auth** - Authentication service database
- **postgres-saves** - Save service database
- **redis** - Caching and session storage

### Application Services
- **auth-service** - Port 3001
- **game-engine-service** - Port 3002
- **story-service** - Port 3003
- **save-service** - Port 3004
- **frontend** - Port 3000

### Infrastructure
- **nginx** - Reverse proxy (Port 80)
- **postgres** - Database cluster

## Quick Start

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up --build -d
```

## Environment Variables

Copy the example environment files and configure:

```bash
cp env/.env.example .env
# Edit .env with your configuration
```

## Database Management

```bash
# Access postgres
docker-compose exec postgres-auth psql -U nuclearstory -d auth_db

# Run migrations
docker-compose exec auth-service npm run migration:run
```
