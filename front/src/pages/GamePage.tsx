import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'
import { useGameStore } from '../store/gameStore'
import GameStats from '../components/GameStats'
import GameInventory from '../components/GameInventory'
import GameStory from '../components/GameStory'

const GamePage = () => {
  const { isAuthenticated } = useAuthStore()
  const { 
    isGameActive, 
    startGame, 
    endGame, 
    currentLocation, 
    currentQuest 
  } = useGameStore()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login')
    }
  }, [isAuthenticated, navigate])

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid lg:grid-cols-4 gap-6">
        {/* Game Stats Panel */}
        <div className="lg:col-span-1">
          <GameStats />
          <div className="mt-6">
            <GameInventory />
          </div>
        </div>

        {/* Main Game Area */}
        <div className="lg:col-span-3">
          {!isGameActive ? (
            <div className="bg-card p-8 rounded-lg border text-center">
              <h2 className="text-3xl font-bold mb-4 text-primary">
                Welcome to the Wasteland
              </h2>
              <p className="text-muted-foreground mb-6">
                You wake up in an underground bunker. The world above has been devastated by nuclear war.
                Your survival depends on the choices you make.
              </p>
              <button
                onClick={startGame}
                className="px-8 py-4 bg-primary text-primary-foreground text-lg font-semibold rounded-lg hover:bg-primary/90 transition-colors"
              >
                Begin Your Survival
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Game Header */}
              <div className="bg-card p-4 rounded-lg border">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-xl font-semibold">
                      📍 {currentLocation.replace('_', ' ').toUpperCase()}
                    </h2>
                    {currentQuest && (
                      <p className="text-muted-foreground">
                        Quest: {currentQuest.replace('_', ' ')}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={endGame}
                    className="px-4 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors"
                  >
                    End Game
                  </button>
                </div>
              </div>

              {/* Story Component */}
              <GameStory />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default GamePage
