# Shared - NuclearStory

Common models, types, and utilities shared between frontend and backend services.

## Contents

### Types
- `types/` - TypeScript type definitions
  - `user.types.ts` - User and authentication types
  - `game.types.ts` - Game state and mechanics types
  - `story.types.ts` - Story and narrative types
  - `api.types.ts` - API request/response types

### Models
- `models/` - Data models and interfaces
  - `User.ts` - User entity model
  - `GameState.ts` - Game state model
  - `Quest.ts` - Quest and event models
  - `Save.ts` - Save data model

### Utils
- `utils/` - Utility functions
  - `validation.ts` - Data validation helpers
  - `constants.ts` - Application constants
  - `helpers.ts` - Common helper functions

### Enums
- `enums/` - Shared enumerations
  - `GameStatus.ts` - Game status values
  - `UserRole.ts` - User role definitions
  - `EventType.ts` - Game event types

## Usage

### Frontend
```typescript
import { User, GameState } from '@shared/types';
import { validateEmail } from '@shared/utils';
```

### Backend
```typescript
import { UserRole } from '@shared/enums';
import { CreateUserDto } from '@shared/models';
```

## Development

This package is designed to be imported by both frontend and backend services.
Any changes here should be compatible with both environments.

### Building
```bash
npm run build    # Compile TypeScript
npm run watch    # Watch mode for development
```
