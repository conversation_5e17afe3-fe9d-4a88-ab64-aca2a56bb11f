# Backend - NuclearStory

Microservices backend for the NuclearStory game built with NestJS.

## Architecture

The backend consists of four main microservices:

### 🔐 auth-service (Port 3001)
- User registration and authentication
- JWT token management
- Role-based access control
- PostgreSQL for user data

### 🎮 game-engine-service (Port 3002)
- Game logic and mechanics
- Survival variables (hunger, thirst, radiation, health)
- Quest system and events
- Game state management

### 📖 story-service (Port 3003)
- AI-powered story generation
- Integration with LLM APIs (OpenAI/Hugging Face)
- Narrative content management
- Dynamic story branching

### 💾 save-service (Port 3004)
- Game save management
- Player progress tracking
- Multiple save slots
- PostgreSQL for save data

## Tech Stack

- **NestJS** - Node.js framework
- **TypeScript** - Type safety
- **PostgreSQL** - Primary database
- **TypeORM** - Database ORM
- **JWT** - Authentication tokens
- **Docker** - Containerization

## Getting Started

Each service can be run independently:

```bash
cd auth-service
npm install
npm run start:dev
```

## Development

All services follow the same structure:
- `src/` - Source code
- `test/` - Unit and integration tests
- `Dockerfile` - Container configuration
- `.env.example` - Environment variables template

## API Documentation

Each service provides Swagger documentation at `/api/docs` endpoint.
