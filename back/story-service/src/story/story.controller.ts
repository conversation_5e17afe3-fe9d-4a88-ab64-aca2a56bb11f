import { <PERSON>, Post, Body, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { StoryService } from './story.service';
import { GenerateStoryDto, GenerateQuestDto, GenerateDialogueDto, GenerateEventDto } from './dto/story.dto';

@ApiTags('Story Generation')
@Controller('story')
export class StoryController {
  constructor(private readonly storyService: StoryService) {}

  @Post('generate')
  @ApiOperation({ summary: 'Generate story content' })
  @ApiResponse({ status: 200, description: 'Story generated successfully' })
  async generateStory(@Body() generateStoryDto: GenerateStoryDto) {
    return this.storyService.generateStory(generateStoryDto);
  }

  @Post('quest')
  @ApiOperation({ summary: 'Generate quest content' })
  @ApiResponse({ status: 200, description: 'Quest generated successfully' })
  async generateQuest(@Body() generateQuestDto: GenerateQuestDto) {
    return this.storyService.generateQuest(generateQuestDto);
  }

  @Post('dialogue')
  @ApiOperation({ summary: 'Generate NPC dialogue' })
  @ApiResponse({ status: 200, description: 'Dialogue generated successfully' })
  async generateDialogue(@Body() generateDialogueDto: GenerateDialogueDto) {
    return this.storyService.generateDialogue(generateDialogueDto);
  }

  @Post('event')
  @ApiOperation({ summary: 'Generate random event' })
  @ApiResponse({ status: 200, description: 'Event generated successfully' })
  async generateEvent(@Body() generateEventDto: GenerateEventDto) {
    return this.storyService.generateEvent(generateEventDto);
  }

  @Get('templates/:type')
  @ApiOperation({ summary: 'Get story templates' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates(@Param('type') type: string) {
    return this.storyService.getTemplates(type);
  }
}
