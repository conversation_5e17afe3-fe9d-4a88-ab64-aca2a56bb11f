# NuclearStory

A post-apocalyptic survival game with AI-driven storytelling and dynamic quest generation.

## 🎮 About

NuclearStory is an interactive survival game set in a post-nuclear world where players make critical decisions that affect their survival and the story's progression. The game features AI-generated narratives, dynamic events, and a complex survival system.

## 🏗️ Architecture

This project follows a microservices architecture with the following structure:

```
nuclearstory/
├── front/              # React + Vite frontend
├── back/               # Backend microservices
│   ├── auth-service/   # Authentication & user management
│   ├── game-engine-service/  # Game logic & survival mechanics
│   ├── story-service/  # AI story generation
│   └── save-service/   # Game saves & progress
├── infra/              # Docker, nginx, environment configs
├── docs/               # Database schemas, API specs
└── shared/             # Common models & utilities
```

## 🛠️ Tech Stack

### Frontend
- **React** with **TypeScript**
- **Vite** for build tooling
- **React Router** for navigation
- **Zustand** for state management
- **Tailwind CSS** + **shadcn/ui** for styling
- **Axios** for API communication

### Backend
- **Node.js** with **TypeScript**
- **NestJS** framework for microservices
- **PostgreSQL** for data persistence
- **TypeORM** for database management
- **JWT** for authentication
- **Docker** for containerization

### Infrastructure
- **Docker Compose** for orchestration
- **nginx** for reverse proxy
- **PostgreSQL** databases per service

## 🚀 Getting Started

### Prerequisites
- Node.js 22+ (рекомендуется)
- Docker & Docker Compose
- Git

### Quick Start (Docker)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nuclearstory
   ```

2. **Setup environment variables**
   ```bash
   cp infra/.env.example infra/.env
   # Edit infra/.env with your configuration (especially OPENAI_API_KEY)
   ```

3. **Build and start all services**
   ```bash
   cd infra
   ./scripts/build.sh
   ./scripts/start.sh
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - API Gateway: http://localhost:80
   - Auth Service: http://localhost:3001/api/docs
   - Game Engine: http://localhost:3002/api/docs
   - Story Service: http://localhost:3003/api/docs
   - Save Service: http://localhost:3004/api/docs

5. **Check status and manage**
   ```bash
   ./scripts/status.sh    # Check service health
   ./scripts/stop.sh      # Stop all services
   ./scripts/dev.sh       # Development mode
   ```

### Development Setup (Local)

1. **Setup frontend**
   ```bash
   cd front
   npm install
   npm run dev
   ```

2. **Setup backend services**
   ```bash
   # Start databases
   cd infra
   docker-compose up postgres-auth postgres-saves redis -d

   # Start auth service
   cd ../back/auth-service
   cp .env.example .env
   npm install
   npm run start:dev

   # Repeat for other services...
   ```

## 📝 Development

### Frontend Development
```bash
cd front
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
```

### Backend Development
Each microservice can be run independently:
```bash
cd back/auth-service
npm run start:dev    # Start in development mode
```

## 🎯 Game Features

- **Survival Mechanics**: Manage hunger, thirst, radiation, and health
- **AI Storytelling**: Dynamic narrative generation based on player choices
- **Quest System**: Procedurally generated quests and events
- **Character Progression**: Skills, inventory, and relationship systems
- **Save System**: Multiple save slots with progress tracking

## 📚 API Documentation

API documentation will be available at:
- Auth Service: `http://localhost:3001/api/docs`
- Game Engine: `http://localhost:3002/api/docs`
- Story Service: `http://localhost:3003/api/docs`
- Save Service: `http://localhost:3004/api/docs`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
